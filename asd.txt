update_ram, bank=0, chip=0
write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

on_select    chip=1
update_ram, bank=0, chip=1
write   [['0', '', '0', '0', '0', '0', '0', '0', '0', '0', '16', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '12'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

update_ram, bank=0, chip=0
write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

on_select    chip=2
update_ram, bank=0, chip=2
write   [['0', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '11', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '22', '', '0', '0', '0', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '8'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

on_select    chip=2
update_ram, bank=0, chip=2
write   [['0', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '11', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '22', '', '0', '0', '0', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '8'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

on_select    chip=3
write   [['0', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '5', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '6', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

on_select    chip=1
write   [['0', '', '0', '0', '0', '0', '0', '0', '0', '0', '16', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '12'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

write   [['0', '', '0', '0', '0', '0', '0', '10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['1', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '13', '0', '', '0', '0', '7', '0'], ['2', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0'], ['3', '', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '', '0', '0', '0', '0']]

return  [Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False), Row(style=None, end_section=False)]

