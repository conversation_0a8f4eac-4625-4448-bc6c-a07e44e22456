#!/usr/bin/env python3
"""
Test script to verify that table content changes when bank/chip selection changes.
"""

from data import ram


def test_table_changes():
    """Test that different bank/chip combinations produce different table content."""

    print("Testing table content changes for different bank/chip combinations:")
    print("=" * 70)

    # Test a few different combinations
    combinations = [
        (0, 0),
        (0, 1),
        (0, 2),
        (0, 3),
        (1, 0),
        (1, 1),
        (1, 2),
        (1, 3),
        (2, 0),
        (2, 1),
        (2, 2),
        (2, 3),
        (3, 0),
        (3, 1),
        (3, 2),
        (3, 3),
    ]

    results = {}

    for bank, chip in combinations:
        # Extract some key data points to compare
        # We'll look at the RAM data directly from the source
        ram_data = ram[bank][chip]
        data_summary = []

        # Get the actual data from the RAM object
        for i in range(4):  # 4 rows
            data_row = [str(ram_data.data[i][j]) for j in range(16)]  # 16 data columns
            status_row = [
                str(ram_data.status[i][j]) for j in range(4)
            ]  # 4 status columns
            data_summary.append((data_row, status_row))

        results[(bank, chip)] = data_summary

        # Print a summary for this combination
        print(f"Bank {bank}, Chip {chip}:")
        for i, (data_row, status_row) in enumerate(data_summary):
            # Only show non-zero values to make it easier to see differences
            non_zero_data = [(j, val) for j, val in enumerate(data_row) if val != "0"]
            non_zero_status = [
                (j, val) for j, val in enumerate(status_row) if val != "0"
            ]

            if non_zero_data or non_zero_status:
                print(f"  Row {i}: Data={non_zero_data}, Status={non_zero_status}")

        if not any(
            any(val != "0" for val in data_row + status_row)
            for data_row, status_row in data_summary
        ):
            print(f"  (All zeros)")
        print()

    # Check if we have different results for different combinations
    unique_results = set()
    for key, result in results.items():
        # Convert to a hashable format
        result_tuple = tuple(
            tuple(tuple(row) for row in [data_row, status_row])
            for data_row, status_row in result
        )
        unique_results.add(result_tuple)

    print(
        f"Summary: Found {len(unique_results)} unique table configurations out of {len(combinations)} combinations."
    )

    if len(unique_results) > 1:
        print("✅ SUCCESS: Tables change based on bank/chip selection!")
    else:
        print("❌ ISSUE: All tables appear to be the same.")

    return len(unique_results) > 1


if __name__ == "__main__":
    test_table_changes()
