from pprint import pprint


class RAM:
    def __init__(self):
        self.data = [[0 for _ in range(16)] for _ in range(4)]
        self.status = [[0 for _ in range(4)] for _ in range(4)]

    def __repr__(self):
        return f"Data: {self.data}, Status: {self.status}"


ram = [[RAM() for _ in range(4)] for _ in range(4)]


ram[0][0].data[1][14] = 13
ram[0][0].data[0][5] = 10
ram[1][2].data[0][8] = 7
ram[2][3].data[0][15] = 22
ram[3][1].data[0][4] = 18
ram[0][3].data[0][11] = 5
ram[2][0].data[0][3] = 9
ram[1][1].data[0][7] = 15
ram[3][2].data[0][12] = 6
ram[0][2].data[0][9] = 11
ram[2][1].data[0][14] = 20
ram[1][3].data[0][2] = 8
ram[3][0].data[0][10] = 14
ram[2][2].data[0][13] = 19
ram[1][0].data[0][6] = 12
ram[3][3].data[0][15] = 21
ram[0][1].data[0][8] = 16
ram[2][2].data[0][11] = 17
ram[1][2].data[0][9] = 15
ram[3][1].data[0][13] = 23
ram[0][3].data[2][7] = 4
ram[1][1].data[3][12] = 11
ram[2][0].data[1][5] = 8
ram[3][2].data[2][14] = 25
ram[1][3].data[3][1] = 3
ram[2][1].data[1][9] = 18
ram[3][0].data[2][6] = 7
ram[0][2].data[3][10] = 13
ram[1][0].data[2][3] = 6
ram[2][3].data[1][11] = 14
ram[3][1].data[3][8] = 9
ram[0][2].data[1][15] = 22
ram[2][2].data[3][4] = 17
ram[0][0].status[1][2] = 7
ram[0][1].status[0][3] = 12
ram[1][2].status[2][1] = 3
ram[2][3].status[3][0] = 9
ram[3][1].status[1][1] = 15
ram[0][3].status[2][2] = 6
ram[2][0].status[0][0] = 11
ram[1][1].status[3][3] = 2
ram[3][2].status[1][0] = 14
ram[0][2].status[2][3] = 8
ram[2][1].status[0][1] = 5
ram[1][3].status[1][2] = 13
ram[3][0].status[3][1] = 1
ram[2][2].status[0][2] = 10
ram[1][0].status[2][0] = 4
ram[3][3].status[1][3] = 0
