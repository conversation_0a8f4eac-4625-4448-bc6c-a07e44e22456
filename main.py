from rich.console import Console
from rich.table import Table
from rich.box import ASCII
from data import ram

console1 = Console()

table1 = Table(
    show_header=True,
    header_style="magenta",
    show_edge=False,
    show_lines=False,
    box=ASCII,
)
table1.add_column(header="REG", width=3, overflow="crop")
table1.add_column(max_width=1)
table1.add_column("CHAR", width=77, justify="center")
table1.add_column(max_width=1)
table1.add_column("STATUS", width=17, justify="center")

# console1.print(table1)

console2 = Console()
table2 = Table(
    show_header=True,
    box=ASCII,
    header_style="blue",
    show_edge=False,
    row_styles=["none", "dim"],
)

table2.add_column("", width=3, justify="center")
table2.add_column("")
table2.add_column("0", width=2)
table2.add_column("1", width=2)
table2.add_column("2", width=2)
table2.add_column("3", width=2)
table2.add_column("4", width=2)
table2.add_column("5", width=2)
table2.add_column("6", width=2)
table2.add_column("7", width=2)
table2.add_column("8", width=2)
table2.add_column("9", width=2)
table2.add_column("A", width=2)
table2.add_column("B", width=2)
table2.add_column("C", width=2)
table2.add_column("D", width=2)
table2.add_column("E", width=2)
table2.add_column("F", width=2)
table2.add_column("")
table2.add_column("0", width=2)
table2.add_column("1", width=2)
table2.add_column("2", width=2)
table2.add_column("3", width=2)


def main2(m):
    tt = []
    for i, r in enumerate(m.data):
        t = []
        t.append(f"{i}")
        t.append("")
        for j in range(16):
            t.append(str(r[j]))
        t.append("")
        for j in range(4):
            t.append(str(m.status[i][j]))
        tt.append(t)
    return tt


def t2(b, c):
    # Create a new table instance each time
    new_table = Table(
        show_header=True,
        box=ASCII,
        header_style="blue",
        show_edge=False,
        row_styles=["none", "dim"],
    )

    # Add columns to the new table
    new_table.add_column("", width=3, justify="center")
    new_table.add_column("")
    new_table.add_column("0", width=2)
    new_table.add_column("1", width=2)
    new_table.add_column("2", width=2)
    new_table.add_column("3", width=2)
    new_table.add_column("4", width=2)
    new_table.add_column("5", width=2)
    new_table.add_column("6", width=2)
    new_table.add_column("7", width=2)
    new_table.add_column("8", width=2)
    new_table.add_column("9", width=2)
    new_table.add_column("A", width=2)
    new_table.add_column("B", width=2)
    new_table.add_column("C", width=2)
    new_table.add_column("D", width=2)
    new_table.add_column("E", width=2)
    new_table.add_column("F", width=2)
    new_table.add_column("")
    new_table.add_column("0", width=2)
    new_table.add_column("1", width=2)
    new_table.add_column("2", width=2)
    new_table.add_column("3", width=2)

    # Get data and add rows
    r = main2(ram[b][c])
    for i in r:
        new_table.add_row(*i)

    return new_table


# table2.add_row(
#     "0",
#     "",
#     "A",
#     "B",
#     "C",
#     "D",
#     "E",
#     "F",
#     "G",
#     "H",
#     "I",
#     "J",
#     "K",
#     "L",
#     "M",
#     "N",
#     "O",
#     "P",
#     "",
#     "6",
#     "3",
#     "5",
#     "3",
#     style="italic",
# )

# console2.print(table2)
