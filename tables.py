from textual.app import App
from textual.widgets import <PERSON><PERSON>, Footer
from textual import on
from textual.app import App, ComposeResult
from textual.reactive import reactive
from textual.widgets import <PERSON><PERSON>, <PERSON>er, Select, Static
from textual.containers import Horizontal
from main import table1, t2


class Tables(Static):
    bank = 0
    chip = 0

    def compose(self):
        yield Horizontal(
            Select([(str(i), str(i)) for i in range(4)], id="bank", prompt="Bank"),
            Select([(str(i), str(i)) for i in range(4)], id="chip", prompt="Chip"),
        )
        yield Static(table1, id="headers")
        yield Static(id="ram")

    def on_mount(self):
        self.update_ram()

    def on_select_changed(self, event: Select.Changed):
        with open("asd.txt", "a") as f:
            f.write(f"on_select    {event.select.id}={event.value}\n")
        if event.select.id == "bank":
            self.bank = int(event.value)
        elif event.select.id == "chip":
            self.chip = int(event.value)
        self.update_ram()

    def update_ram(self):
        with open("asd.txt", "a") as f:
            f.write(f"update_ram, bank={self.bank}, chip={self.chip}\n")
        try:
            self.query("#ram").remove()
        except:
            pass
        self.mount(Static(t2(self.bank, self.chip), id="ram"))


class HeaderApp(App):
    BINDINGS = [
        ("d", "toggle_dark", "Toggle Dark Mode"),
        ("q", "quit", "Quit"),
    ]
    CSS_PATH = "tables.css"

    def compose(self) -> ComposeResult:
        yield Header()
        yield Footer()
        yield Tables()


if __name__ == "__main__":
    app = HeaderApp()
    app.run()
