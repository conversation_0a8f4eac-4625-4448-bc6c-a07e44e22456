from textual.app import App
from textual.widgets import <PERSON><PERSON>, Footer
from textual import on
from textual.app import App, ComposeResult
from textual.reactive import reactive
from textual.widgets import <PERSON><PERSON>, <PERSON>er, Select, Static
from textual.containers import Horizontal
from main import table1, t2


class Tables(Static):
    bank = 0
    chip = 0

    def compose(self):
        yield Horizontal(
            Select([(str(i), str(i)) for i in range(4)], id="bank", prompt="Bank"),
            Select([(str(i), str(i)) for i in range(4)], id="chip", prompt="Chip"),
        )
        yield Static(table1, id="headers")
        yield Static(id="ram")

    def on_mount(self):
        self.update_ram()

    def on_select_changed(self, event: Select.Changed):
        if event.select.id == "bank":
            self.bank = int(event.value)
        elif event.select.id == "chip":
            self.chip = int(event.value)
        self.update_ram()

    def update_ram(self):
        # Update the content of the existing ram widget instead of replacing it
        ram_widget = self.query_one("#ram", Static)
        ram_widget.update(t2(self.bank, self.chip))


class HeaderApp(App):
    BINDINGS = [
        ("d", "toggle_dark", "Toggle Dark Mode"),
        ("q", "quit", "Quit"),
    ]
    CSS_PATH = "tables.css"

    def compose(self) -> ComposeResult:
        yield Header()
        yield Footer()
        yield Tables()


if __name__ == "__main__":
    app = HeaderApp()
    app.run()
